{"name": "remix-business-matchmaking", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev --host 0.0.0.0", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@react-router/node": "^7.7.1", "@react-router/serve": "^7.7.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "isbot": "^5.1.27", "lucide-react": "^0.540.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.7.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@react-router/dev": "^7.7.1", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.3.7", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}}