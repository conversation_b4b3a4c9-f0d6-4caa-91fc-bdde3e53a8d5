import { type RouteConfig, index, route } from "@react-router/dev/routes";

export default [
  index("routes/home.tsx"),
  route("login", "routes/login.tsx"),
  route("buyer-questionaries1", "routes/buyer-questionaries1.tsx"),
  route("buyer-questionaries2", "routes/buyer-questionaries2.tsx"),
  route("buyer-questionaries3", "routes/buyer-questionaries3.tsx"),
  route("buyer-questionaries4", "routes/buyer-questionaries4.tsx"),
  route("buyer-questionaries5", "routes/buyer-questionaries5.tsx"),
  route("buyer-discovery", "routes/buyer-discovery.tsx"),
] satisfies RouteConfig;
