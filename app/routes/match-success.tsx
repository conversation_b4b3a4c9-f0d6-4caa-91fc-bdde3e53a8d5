import type { LoaderFunctionArgs, MetaFunction } from "react-router";
import { useLoaderData, useNavigate } from "react-router";
import { MatchSuccess } from "~/components/match-success";
import type { Route } from "./+types/match-success";

export const meta: MetaFunction = () => {
  return [
    { title: "Match Success - Business Matchmaking" },
    {
      name: "description",
      content: "You've found a match! Start discussing your business deal.",
    },
  ];
};

interface MatchUser {
  id: string;
  name: string;
  image?: string;
  role: "buyer" | "seller";
}

export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const buyerId = url.searchParams.get("buyerId");
  const sellerId = url.searchParams.get("sellerId");

  // In a real app, you would fetch the actual user data from your database
  // For now, we'll use mock data
  const mockBuyer: MatchUser = {
    id: buyerId || "1",
    name: "<PERSON>",
    role: "buyer",
    // image: "/images/maria.jpg" // Add actual image URLs when available
  };

  const mockSeller: MatchUser = {
    id: sellerId || "2",
    name: "<PERSON>",
    role: "seller",
    // image: "/images/jake.jpg" // Add actual image URLs when available
  };
}

export default function MatchSuccessRoute({
  buyer,
  seller,
}: Route.ComponentProps) {
  const { buyer, seller } = LoaderData;
  const navigate = useNavigate();

  const handleGoToDashboard = () => {
    // Navigate to deal dashboard with match information
    navigate(`/dashboard?matchId=${buyer.id}-${seller.id}`);
  };

  const handleDiscoverMore = () => {
    // Navigate back to discover page
    navigate("/discover");
  };

  return (
    <MatchSuccess
      buyer={buyer}
      seller={seller}
      onGoToDashboard={handleGoToDashboard}
      onDiscoverMore={handleDiscoverMore}
    />
  );
}
