import { useNavigate } from "react-router";
import { DealDashboard } from "~/components/deal-dashboard";
import type { Route } from "./+types/dashboard";
import User1Image from "~/assets/Dan.jpg";
import User2Image from "~/assets/Maria.jpg";

export const meta: Route.MetaFunction = () => {
  return [
    { title: "Deal Dashboard - Business Matchmaking" },
    {
      name: "description",
      content: "Track your business deal progress and manage communications.",
    },
  ];
};

interface Contact {
  id: string;
  name: string;
  role: "buyer" | "seller";
  image?: string;
}

interface Milestone {
  id: string;
  title: string;
  status: "completed" | "in-progress" | "pending";
  order: number;
}

interface DealData {
  id: string;
  name: string;
  businessDescription: string;
  valuationRange: string;
  stage: string;
  progress: number;
  nextStep: {
    title: string;
    status: "On Progress" | "Completed" | "Pending";
  };
  contacts: Contact[];
  milestones: Milestone[];
}

export async function loader({ request }: Route.LoaderArgs) {
  const url = new URL(request.url);
  const matchId = url.searchParams.get("matchId");
  const dealId = url.searchParams.get("dealId");

  // In a real app, you would fetch the actual deal data from your database
  // For now, we'll use mock data based on the match or deal ID
  const mockDeal: DealData = {
    id: dealId || matchId || "1",
    name: "Acquisition of tech Startup",
    businessDescription: "Inovative SaaS Platform",
    valuationRange: "$4M - $7M",
    stage: "NDA",
    progress: 25,
    nextStep: {
      title: "Due Diligence",
      status: "On Progress"
    },
    contacts: [
      {
        id: "1",
        name: "Jake",
        role: "seller",
        image: User1Image
      },
      {
        id: "2",
        name: "Maria",
        role: "buyer",
        image: User2Image
      }
    ],
    milestones: [
      {
        id: "1",
        title: "Intro",
        status: "completed",
        order: 1
      },
      {
        id: "2",
        title: "NDA",
        status: "completed",
        order: 2
      },
      {
        id: "3",
        title: "Due Diligence",
        status: "in-progress",
        order: 3
      },
      {
        id: "4",
        title: "Valuation",
        status: "pending",
        order: 4
      },
      {
        id: "5",
        title: "LOI",
        status: "pending",
        order: 5
      },
      {
        id: "6",
        title: "Contract",
        status: "pending",
        order: 6
      },
      {
        id: "7",
        title: "Payment",
        status: "pending",
        order: 7
      },
      {
        id: "8",
        title: "Closing",
        status: "pending",
        order: 8
      }
    ]
  };

  return { deal: mockDeal };
}

export default function DashboardRoute({
  loaderData,
}: Route.ComponentProps) {
  const { deal } = loaderData;
  const navigate = useNavigate();

  const handleBack = () => {
    // Navigate back to previous page or home
    navigate(-1);
  };

  return (
    <DealDashboard
      deal={deal}
      onBack={handleBack}
    />
  );
}
